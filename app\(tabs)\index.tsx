import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Typography } from '@/constants/Colors';
import { useGlobalContext } from '@/contexts/GlobalContext';
import { useCategories } from '@/hooks/useCategories';
import { useFeaturedProducts } from '@/hooks/useProducts';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { ActivityIndicator, Alert, FlatList, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

export default function HomeScreen() {
  const { products: featuredProducts, loading: productsLoading } = useFeaturedProducts();
  const { categories, loading: categoriesLoading } = useCategories();
  const { addToCart, isLoggedIn } = useGlobalContext();

  const handleAddToCart = async (product: any) => {
    if (!isLoggedIn) {
      Alert.alert('Sign In Required', 'Please sign in to add items to your cart', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign In', onPress: () => router.push('/(tabs)/profile') }
      ]);
      return;
    }

    try {
      await addToCart(
        product.$id,
        product.name,
        product.price,
        product.image,
        product.unit
      );
      Alert.alert('Success', `${product.name} added to cart!`);
    } catch (err) {
      console.error('Failed to add to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  const renderProduct = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.productCard}
      onPress={() => router.push(`/product/${item.$id}`)}
    >
      {item.discount && (
        <ThemedView style={styles.discountBadge}>
          <ThemedText style={styles.discountText}>{item.discount}% OFF</ThemedText>
        </ThemedView>
      )}
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <ThemedView style={styles.productInfo}>
        <ThemedText type="defaultSemiBold" numberOfLines={2}>{item.name}</ThemedText>
        <ThemedText style={styles.productPrice}>KSh {item.price}/{item.unit}</ThemedText>
        <TouchableOpacity 
          style={styles.addButton}
          activeOpacity={0.8}
          onPress={(e) => {
            e.stopPropagation();
            handleAddToCart(item);
          }}
        >
          <IconSymbol name="plus" size={16} color="white" />
        </TouchableOpacity>
      </ThemedView>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerLeft}>
          <Image 
            source={require('@/assets/images/jujafresh-logo.jpeg')} 
            style={styles.logo}
          />
          <ThemedView style={styles.headerText}>
            <ThemedText style={styles.greeting}>Good morning! 👋</ThemedText>
            <ThemedText type="title">What do you need today?</ThemedText>
            <ThemedView style={styles.locationContainer}>
              <IconSymbol name="location.fill" size={16} color="#4CAF50" />
              <ThemedText style={styles.location}>Delivering to Juja, Kiambu</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>
        <TouchableOpacity style={styles.notificationButton}>
          <IconSymbol name="bell" size={24} color="#666" />
        </TouchableOpacity>
      </ThemedView>

      {/* Search Bar */}
      <ThemedView style={styles.searchSection}>
        <TouchableOpacity 
          style={styles.searchBar}
          onPress={() => router.push('/(tabs)/search')}
        >
          <IconSymbol name="magnifyingglass" size={20} color="#666" />
          <ThemedText style={styles.searchPlaceholder}>Search for groceries...</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {/* Quick Categories */}
      <ThemedView style={styles.section}>
        <ThemedText type="subtitle">Shop by Category</ThemedText>
        {categoriesLoading ? (
          <ActivityIndicator size="small" color="#4CAF50" style={{ marginTop: 16 }} />
        ) : (
          <ThemedView style={styles.categoriesRow}>
            {categories.slice(0, 4).map((category) => (
              <TouchableOpacity 
                key={category._id}
                style={styles.categoryItem}
                onPress={() => router.push('/(tabs)/categories')}
              >
                <ThemedView style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                  <IconSymbol name={category.icon as any} size={24} color="white" />
                </ThemedView>
                <ThemedText style={styles.categoryName}>{category.name}</ThemedText>
              </TouchableOpacity>
            ))}
          </ThemedView>
        )}
      </ThemedView>

      {/* Banner */}
      <ThemedView style={styles.section}>
        <TouchableOpacity style={styles.banner}>
          <ThemedView style={styles.bannerContent}>
            <ThemedText type="subtitle" style={styles.bannerTitle}>
              Free Delivery on Orders Over KSh 1,000
            </ThemedText>
            <ThemedText style={styles.bannerSubtitle}>
              Get your groceries delivered for free within Juja area
            </ThemedText>
          </ThemedView>
          <ThemedText style={styles.bannerEmoji}>🚚</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {/* Featured Products */}
      <ThemedView style={styles.section}>
        <ThemedView style={styles.sectionHeader}>
          <ThemedText type="subtitle">Featured Products</ThemedText>
          <TouchableOpacity>
            <ThemedText style={styles.seeAll}>See All</ThemedText>
          </TouchableOpacity>
        </ThemedView>
        {productsLoading ? (
          <ActivityIndicator size="small" color="#4CAF50" style={{ marginTop: 16 }} />
        ) : (
          <FlatList
            data={featuredProducts}
            renderItem={renderProduct}
            keyExtractor={(item) => item._id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.productsContainer}
          />
        )}
      </ThemedView>

      {/* Fresh Deals */}
      <ThemedView style={styles.section}>
        <ThemedText type="subtitle">Fresh Deals</ThemedText>
        <ThemedView style={styles.dealsGrid}>
          <TouchableOpacity style={styles.dealCard}>
            <ThemedText style={styles.dealTitle}>Buy 2 Get 1 Free</ThemedText>
            <ThemedText style={styles.dealSubtitle}>On selected fruits</ThemedText>
            <ThemedText style={styles.dealEmoji}>🍎🍊🍌</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.dealCard}>
            <ThemedText style={styles.dealTitle}>30% Off</ThemedText>
            <ThemedText style={styles.dealSubtitle}>Dairy products</ThemedText>
            <ThemedText style={styles.dealEmoji}>🥛🧀</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.dark.surface,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    opacity: 0.7,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4,
  },
  location: {
    fontSize: 14,
    opacity: 0.8,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  notificationButton: {
    padding: 8,
  },
  searchSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.dark.surfaceSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  searchPlaceholder: {
    opacity: 0.6,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeAll: {
    color: Colors.dark.success,
    fontSize: 14,
    fontFamily: Typography.fontFamily.medium,
  },
  categoriesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  categoryItem: {
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    textAlign: 'center',
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.medium,
  },
  banner: {
    flexDirection: 'row',
    backgroundColor: Colors.dark.tintSecondary,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  bannerContent: {
    flex: 1,
  },
  bannerTitle: {
    color: Colors.dark.success,
    fontFamily: Typography.fontFamily.semibold,
  },
  bannerSubtitle: {
    color: Colors.dark.success,
    opacity: 0.8,
    marginTop: 4,
    fontSize: 14,
    fontFamily: Typography.fontFamily.regular,
  },
  bannerEmoji: {
    fontSize: 32,
  },
  productsContainer: {
    paddingRight: 20,
  },
  productCard: {
    width: 140,
    marginRight: 16,
    backgroundColor: Colors.dark.surface,
    borderRadius: 12,
    padding: 12,
    position: 'relative',
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FF5722',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    zIndex: 1,
  },
  discountText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  productImage: {
    width: '100%',
    height: 80,
    borderRadius: 8,
    marginBottom: 8,
  },
  productInfo: {
    position: 'relative',
  },
  productPrice: {
    color: Colors.dark.success,
    fontWeight: '600',
    marginTop: 4,
    fontFamily: Typography.fontFamily.semibold,
  },
  addButton: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    backgroundColor: Colors.dark.success,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dealsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  dealCard: {
    flex: 1,
    backgroundColor: Colors.dark.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.dark.warning,
  },
  dealTitle: {
    fontWeight: '600',
    color: Colors.dark.warning,
    fontFamily: Typography.fontFamily.semibold,
  },
  dealSubtitle: {
    fontSize: 12,
    color: Colors.dark.warning,
    opacity: 0.8,
    marginTop: 4,
    fontFamily: Typography.fontFamily.regular,
  },
  dealEmoji: {
    fontSize: 24,
    marginTop: 8,
  },
});