import { Id } from "../../convex/_generated/dataModel";

export interface Order {
  _id: Id<"orders">;
  userId: string;
  orderNumber: string;
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  total: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'out_for_delivery' | 'delivered' | 'cancelled';
  paymentMethod: 'cash_on_delivery' | 'mpesa' | 'card';
  paymentStatus: 'pending' | 'paid' | 'failed';
  deliveryAddress: DeliveryAddress;
  customerNotes?: string;
  estimatedDeliveryTime?: string;
  _creationTime: number;
}

export interface OrderItem {
  productId: string;
  productName: string;
  productPrice: number;
  productImage: string;
  productUnit: string;
  quantity: number;
}

export interface DeliveryAddress {
  street: string;
  city: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface CreateOrderData {
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: 'cash_on_delivery' | 'mpesa' | 'card';
  deliveryAddress: DeliveryAddress;
  customerNotes?: string;
}

class OrderService {
  private client: any;

  constructor() {
    this.client = null;
  }

  // Initialize with Convex client
  initialize(client: any) {
    this.client = client;
  }

  // Create a new order (requires authentication)
  async createOrder(orderData: CreateOrderData): Promise<Order> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const order = await this.client.mutation("orders:createOrder", orderData);
      return order;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Get user's orders (requires authentication)
  async getUserOrders(): Promise<Order[]> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const orders = await this.client.query("orders:getUserOrders");
      return orders || [];
    } catch (error) {
      console.error('Error fetching user orders:', error);
      return [];
    }
  }

  // Get order by ID
  async getOrderById(orderId: string): Promise<Order> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const order = await this.client.query("orders:getOrderById", { orderId });
      return order;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  // Get order by order number
  async getOrderByNumber(orderNumber: string): Promise<Order | null> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const order = await this.client.query("orders:getOrderByNumber", { orderNumber });
      return order;
    } catch (error) {
      console.error('Error fetching order by number:', error);
      return null;
    }
  }

  // Update order status (admin function)
  async updateOrderStatus(
    orderId: string, 
    status: Order['status'], 
    estimatedDeliveryTime?: string
  ): Promise<Order> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const order = await this.client.mutation("orders:updateOrderStatus", {
        orderId,
        status,
        estimatedDeliveryTime,
      });

      return order;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Update payment status (admin function)
  async updatePaymentStatus(orderId: string, paymentStatus: Order['paymentStatus']): Promise<Order> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const order = await this.client.mutation("orders:updatePaymentStatus", {
        orderId,
        paymentStatus,
      });

      return order;
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }
  }

  // Cancel order
  async cancelOrder(orderId: string): Promise<Order> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const order = await this.client.mutation("orders:cancelOrder", { orderId });
      return order;
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  }

  // Get orders by status (admin function)
  async getOrdersByStatus(status: Order['status']): Promise<Order[]> {
    try {
      if (!this.client) {
        throw new Error('Order service not initialized');
      }

      const orders = await this.client.query("orders:getOrdersByStatus", { status });
      return orders || [];
    } catch (error) {
      console.error('Error fetching orders by status:', error);
      return [];
    }
  }

  // Generate order number (utility function)
  generateOrderNumber(): string {
    const timestamp = Date.now();
    return `JF${timestamp.toString().slice(-8)}`;
  }
}

export default new OrderService();
