import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { Colors, Typography } from "@/constants/Colors";
import { useGlobalContext } from "@/contexts/GlobalContext";
import { useProduct } from "@/hooks/useProducts";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
} from "react-native";

const { width } = Dimensions.get("window");

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { product, loading, error } = useProduct(id || '');
  const { addToCart, isLoggedIn } = useGlobalContext();
  const [quantity, setQuantity] = useState(1);


  const updateQuantity = (change: number) => {
    setQuantity(Math.max(1, quantity + change));
  };

  const handleAddToCart = async () => {
    if (!product) return;
    
    if (!isLoggedIn) {
      Alert.alert('Sign In Required', 'Please sign in to add items to your cart', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign In', onPress: () => router.push('/(tabs)/profile') }
      ]);
      return;
    }

    try {
      await addToCart(
        product._id,
        product.name,
        product.price,
        product.image,
        product.unit,
        quantity
      );
      Alert.alert('Success', `${product.name} added to cart!`, [
        { text: 'Continue Shopping', style: 'cancel' },
        { text: 'View Cart', onPress: () => router.push('/(tabs)/cart') }
      ]);
    } catch (err) {
      console.error('Failed to add to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={24} color="#000" />
          </TouchableOpacity>
        </ThemedView>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <ThemedText style={styles.loadingText}>Loading product...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  if (error || !product) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={24} color="#000" />
          </TouchableOpacity>
        </ThemedView>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText style={styles.errorText}>
            {error || 'Product not found'}
          </ThemedText>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.retryText}>Go Back</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <ThemedView style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={24} color="#000" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.favoriteButton}>
            <IconSymbol name="heart" size={24} color="#FF5722" />
          </TouchableOpacity>
        </ThemedView>

        {/* Product Images */}
        <ThemedView style={styles.imageSection}>
          <Image
            source={{ uri: product.image }}
            style={styles.productImage}
            contentFit="cover"
            placeholder="https://via.placeholder.com/400x300/f0f0f0/cccccc?text=Loading"
            transition={300}
          />
        </ThemedView>

        {/* Product Info */}
        <ThemedView style={styles.productInfo}>
          <ThemedView style={styles.categoryBadge}>
            <ThemedText style={styles.categoryText}>
              {product.category}
            </ThemedText>
          </ThemedView>

          <ThemedText type="title" style={styles.productName}>
            {product.name}
          </ThemedText>

          <ThemedView style={styles.ratingRow}>
            <ThemedView style={styles.stockBadge}>
              <ThemedText style={styles.stockText}>
                {product.inStock ? 'In Stock' : 'Out of Stock'}
              </ThemedText>
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.priceRow}>
            <ThemedText type="title" style={styles.price}>
              KSh {product.price}
            </ThemedText>
            {product.discount && (
              <ThemedText style={styles.originalPrice}>
                KSh {Math.round(product.price / (1 - product.discount / 100))}
              </ThemedText>
            )}
            <ThemedText style={styles.unit}>per {product.unit}</ThemedText>
          </ThemedView>

          {product.description && (
            <ThemedText style={styles.description}>
              {product.description}
            </ThemedText>
          )}

          {/* Stock Info */}
          <ThemedView style={styles.stockSection}>
            <ThemedText type="subtitle">Availability</ThemedText>
            <ThemedView style={styles.stockInfo}>
              <IconSymbol
                name={product.inStock ? "checkmark.circle.fill" : "xmark.circle.fill"}
                size={16}
                color={product.inStock ? "#4CAF50" : "#FF5722"}
              />
              <ThemedText style={styles.stockInfoText}>
                {product.inStock 
                  ? `${product.stockQuantity} ${product.unit} available`
                  : 'Currently out of stock'
                }
              </ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ScrollView>

      {/* Bottom Action Bar */}
      <ThemedView style={styles.bottomBar}>
        <ThemedView style={styles.quantitySection}>
          <ThemedText style={styles.quantityLabel}>Quantity</ThemedText>
          <ThemedView style={styles.quantityControls}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => updateQuantity(-1)}
            >
              <IconSymbol name="minus" size={16} color="#666" />
            </TouchableOpacity>
            <ThemedText style={styles.quantity}>{quantity}</ThemedText>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => updateQuantity(1)}
            >
              <IconSymbol name="plus" size={16} color="#666" />
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>

        <TouchableOpacity 
          style={[
            styles.addToCartButton,
            !product.inStock && styles.disabledButton
          ]} 
          onPress={handleAddToCart}
          disabled={!product.inStock}
        >
          <ThemedText style={[
            styles.addToCartText,
            !product.inStock && styles.disabledText
          ]}>
            {product.inStock 
              ? `Add to Cart • KSh ${(product.price * quantity).toLocaleString()}`
              : 'Out of Stock'
            }
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.dark.surface,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.dark.surface,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  imageSection: {
    position: "relative",
  },
  productImage: {
    width: width,
    height: 300,
  },
  imageIndicators: {
    flexDirection: "row",
    justifyContent: "center",
    position: "absolute",
    bottom: 16,
    left: 0,
    right: 0,
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255,255,255,0.5)",
  },
  activeIndicator: {
    backgroundColor: "white",
  },
  productInfo: {
    padding: 20,
    backgroundColor: Colors.dark.background,
  },
  categoryBadge: {
    alignSelf: "flex-start",
    backgroundColor: Colors.dark.tintSecondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.dark.success,
  },
  categoryText: {
    color: Colors.dark.success,
    fontSize: 12,
    fontWeight: "600",
    fontFamily: Typography.fontFamily.semibold,
  },
  productName: {
    marginBottom: 12,
  },
  ratingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  rating: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  ratingText: {
    fontWeight: "600",
  },
  reviewsText: {
    opacity: 0.6,
    fontSize: 12,
  },
  stockBadge: {
    backgroundColor: Colors.dark.tintSecondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.dark.success,
  },
  stockText: {
    color: Colors.dark.success,
    fontSize: 12,
    fontWeight: "600",
    fontFamily: Typography.fontFamily.semibold,
  },
  priceRow: {
    flexDirection: "row",
    alignItems: "baseline",
    gap: 8,
    marginBottom: 16,
  },
  price: {
    color: Colors.dark.success,
    fontFamily: Typography.fontFamily.bold,
  },
  originalPrice: {
    textDecorationLine: "line-through",
    opacity: 0.6,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  unit: {
    opacity: 0.6,
    fontSize: 14,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  description: {
    lineHeight: 22,
    marginBottom: 24,
    opacity: 0.8,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  featuresSection: {
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginTop: 8,
  },
  featureText: {
    flex: 1,
  },
  nutritionSection: {
    marginBottom: 24,
  },
  nutritionGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
    marginTop: 12,
  },
  nutritionItem: {
    width: "47%",
    backgroundColor: "rgba(0,0,0,0.05)",
    padding: 12,
    borderRadius: 8,
  },
  nutritionLabel: {
    fontSize: 12,
    opacity: 0.6,
  },
  nutritionValue: {
    fontWeight: "600",
    marginTop: 2,
  },
  bottomBar: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.dark.border,
    gap: 16,
    backgroundColor: Colors.dark.surface,
  },
  quantitySection: {
    alignItems: "center",
  },
  quantityLabel: {
    fontSize: 12,
    opacity: 0.6,
    marginBottom: 8,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.dark.surfaceSecondary,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  quantity: {
    fontSize: 16,
    fontWeight: "600",
    minWidth: 20,
    textAlign: "center",
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.semibold,
  },
  addToCartButton: {
    flex: 1,
    backgroundColor: Colors.dark.success,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  addToCartText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    fontFamily: Typography.fontFamily.semibold,
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  disabledText: {
    color: "#666",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 16,
    opacity: 0.6,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  errorText: {
    textAlign: "center",
    color: Colors.dark.error,
    marginBottom: 16,
    fontFamily: Typography.fontFamily.medium,
  },
  retryButton: {
    backgroundColor: Colors.dark.success,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: "white",
    fontWeight: "600",
    fontFamily: Typography.fontFamily.semibold,
  },
  stockSection: {
    marginBottom: 24,
  },
  stockInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginTop: 8,
  },
  stockInfoText: {
    flex: 1,
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.regular,
  },
});
