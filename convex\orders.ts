import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new order
export const createOrder = mutation({
  args: {
    userId: v.string(),
    items: v.array(v.object({
      productId: v.string(),
      productName: v.string(),
      productPrice: v.number(),
      productImage: v.string(),
      productUnit: v.string(),
      quantity: v.number(),
    })),
    subtotal: v.number(),
    deliveryFee: v.number(),
    total: v.number(),
    paymentMethod: v.union(
      v.literal("cash_on_delivery"),
      v.literal("mpesa"),
      v.literal("card")
    ),
    deliveryAddress: v.object({
      street: v.string(),
      city: v.string(),
      postalCode: v.string(),
      country: v.string(),
      coordinates: v.optional(v.object({
        latitude: v.number(),
        longitude: v.number(),
      })),
    }),
    customerNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Generate order number
    const timestamp = Date.now();
    const orderNumber = `JF${timestamp.toString().slice(-8)}`;

    const orderId = await ctx.db.insert("orders", {
      userId: args.userId,
      orderNumber,
      items: args.items,
      subtotal: args.subtotal,
      deliveryFee: args.deliveryFee,
      total: args.total,
      status: "pending",
      paymentMethod: args.paymentMethod,
      paymentStatus: "pending",
      deliveryAddress: args.deliveryAddress,
      customerNotes: args.customerNotes,
      estimatedDeliveryTime: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
    });

    return await ctx.db.get(orderId);
  },
});

// Get user's orders
export const getUserOrders = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();

    return orders;
  },
});

// Get order by ID
export const getOrderById = query({
  args: { orderId: v.id("orders") },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId);
    return order;
  },
});

// Get order by order number
export const getOrderByNumber = query({
  args: { orderNumber: v.string() },
  handler: async (ctx, args) => {
    const order = await ctx.db
      .query("orders")
      .withIndex("by_order_number", (q) => q.eq("orderNumber", args.orderNumber))
      .first();

    return order;
  },
});

// Update order status
export const updateOrderStatus = mutation({
  args: {
    orderId: v.id("orders"),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("preparing"),
      v.literal("out_for_delivery"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    estimatedDeliveryTime: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = { status: args.status };
    
    if (args.estimatedDeliveryTime) {
      updates.estimatedDeliveryTime = args.estimatedDeliveryTime;
    }

    await ctx.db.patch(args.orderId, updates);
    return await ctx.db.get(args.orderId);
  },
});

// Update payment status
export const updatePaymentStatus = mutation({
  args: {
    orderId: v.id("orders"),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("paid"),
      v.literal("failed")
    ),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.orderId, {
      paymentStatus: args.paymentStatus,
    });

    return await ctx.db.get(args.orderId);
  },
});

// Cancel order
export const cancelOrder = mutation({
  args: { orderId: v.id("orders") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.orderId, {
      status: "cancelled",
    });

    return await ctx.db.get(args.orderId);
  },
});

// Get orders by status (admin function)
export const getOrdersByStatus = query({
  args: { 
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("preparing"),
      v.literal("out_for_delivery"),
      v.literal("delivered"),
      v.literal("cancelled")
    )
  },
  handler: async (ctx, args) => {
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_status", (q) => q.eq("status", args.status))
      .order("desc")
      .collect();

    return orders;
  },
});
