import { Id } from "../../convex/_generated/dataModel";

export interface Product {
  _id: Id<"products">;
  name: string;
  description: string;
  price: number;
  unit: string;
  category: string;
  image: string;
  inStock: boolean;
  stockQuantity: number;
  discount?: number;
  featured: boolean;
  _creationTime: number;
}

export interface Category {
  _id: Id<"categories">;
  name: string;
  icon: string;
  color: string;
  description: string;
}

class ProductService {
  private client: any;

  constructor() {
    this.client = null;
  }

  // Initialize with Convex client
  initialize(client: any) {
    this.client = client;
  }

  // Fallback data for when database is not available
  private getFallbackProducts(): Product[] {
    return [
      {
        _id: 'fallback-1' as Id<"products">,
        name: 'Fresh Tomatoes',
        description: 'Locally grown fresh tomatoes',
        price: 150,
        unit: 'kg',
        category: 'vegetables',
        image: 'https://images.unsplash.com/photo-1546470427-e5ac89c8ba3b?w=400',
        inStock: true,
        stockQuantity: 50,
        featured: true,
        _creationTime: Date.now(),
      },
      {
        _id: 'fallback-2' as Id<"products">,
        name: 'Fresh Bananas',
        description: 'Sweet ripe bananas',
        price: 80,
        unit: 'bunch',
        category: 'fruits',
        image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
        inStock: true,
        stockQuantity: 30,
        featured: true,
        _creationTime: Date.now(),
      },
    ];
  }

  private getFallbackCategories(): Category[] {
    return [
      {
        _id: 'fallback-cat-1' as Id<"categories">,
        name: 'Vegetables',
        icon: 'leaf.fill',
        color: '#4CAF50',
        description: 'Fresh vegetables',
      },
      {
        _id: 'fallback-cat-2' as Id<"categories">,
        name: 'Fruits',
        icon: 'apple.logo',
        color: '#FF9800',
        description: 'Fresh fruits',
      },
    ];
  }

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    try {
      if (!this.client) {
        console.log('Product service not initialized, using fallback data');
        return this.getFallbackProducts();
      }

      const products = await this.client.query("products:getAllProducts");
      return products;
    } catch (error) {
      console.error('Error fetching products:', error);
      console.log('Using fallback products data');
      return this.getFallbackProducts();
    }
  }

  // Get featured products
  async getFeaturedProducts(): Promise<Product[]> {
    try {
      if (!this.client) {
        console.log('Product service not initialized, using fallback data');
        return this.getFallbackProducts().filter(p => p.featured);
      }

      const products = await this.client.query("products:getFeaturedProducts");
      return products;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      return this.getFallbackProducts().filter(p => p.featured);
    }
  }

  // Get product by ID
  async getProductById(productId: string): Promise<Product> {
    try {
      if (!this.client) {
        throw new Error('Product service not initialized');
      }

      const product = await this.client.query("products:getProductById", { productId });
      return product;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  // Search products
  async searchProducts(query: string): Promise<Product[]> {
    try {
      if (!this.client) {
        console.log('Product service not initialized, using fallback data');
        return this.getFallbackProducts().filter(p => 
          p.name.toLowerCase().includes(query.toLowerCase())
        );
      }

      const products = await this.client.query("products:searchProducts", { searchTerm: query });
      return products;
    } catch (error) {
      console.error('Error searching products:', error);
      return this.getFallbackProducts().filter(p => 
        p.name.toLowerCase().includes(query.toLowerCase())
      );
    }
  }

  // Get products by category
  async getProductsByCategory(categoryId: string): Promise<Product[]> {
    try {
      if (!this.client) {
        console.log('Product service not initialized, using fallback data');
        return this.getFallbackProducts().filter(p => p.category === categoryId);
      }

      const products = await this.client.query("products:getProductsByCategory", { categoryId });
      return products;
    } catch (error) {
      console.error('Error fetching products by category:', error);
      return this.getFallbackProducts().filter(p => p.category === categoryId);
    }
  }

  // Get all categories
  async getCategories(): Promise<Category[]> {
    try {
      if (!this.client) {
        console.log('Product service not initialized, using fallback data');
        return this.getFallbackCategories();
      }

      const categories = await this.client.query("categories:getAllCategories");
      return categories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      return this.getFallbackCategories();
    }
  }

  // Create product (admin function)
  async createProduct(productData: Omit<Product, '_id' | '_creationTime'>): Promise<Product> {
    try {
      if (!this.client) {
        throw new Error('Product service not initialized');
      }

      const product = await this.client.mutation("products:createProduct", productData);
      return product;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  // Update product
  async updateProduct(productId: string, updates: Partial<Omit<Product, '_id' | '_creationTime'>>): Promise<Product> {
    try {
      if (!this.client) {
        throw new Error('Product service not initialized');
      }

      const product = await this.client.mutation("products:updateProduct", { productId, ...updates });
      return product;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  // Delete product
  async deleteProduct(productId: string): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('Product service not initialized');
      }

      await this.client.mutation("products:deleteProduct", { productId });
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }
}

export default new ProductService();
