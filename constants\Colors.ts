/**
 * JujaFresh Design System Colors
 * Consistent color palette for the entire app
 */

const tintColorLight = '#4CAF50';
const tintColorDark = '#66BB6A';

const Colors = {
  light: {
    text: '#11181C',
    textSecondary: '#687076',
    background: '#FFFFFF',
    backgroundSecondary: '#F8F9FA',
    surface: '#FFFFFF',
    surfaceSecondary: 'rgba(0,0,0,0.05)',
    tint: tintColorLight,
    tintSecondary: '#E8F5E8',
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    border: '#E5E7EB',
    borderLight: '#F3F4F6',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#FF5722',
    info: '#2196F3',
  },
  dark: {
    text: '#ECEDEE',
    textSecondary: '#9BA1A6',
    background: '#151718',
    backgroundSecondary: '#1F2937',
    surface: '#1F2937',
    surfaceSecondary: 'rgba(255,255,255,0.05)',
    tint: tintColorDark,
    tintSecondary: 'rgba(102, 187, 106, 0.1)',
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    border: '#374151',
    borderLight: '#4B5563',
    success: '#66BB6A',
    warning: '#FFB74D',
    error: '#FF7043',
    info: '#42A5F5',
  },
};

// Typography system
const Typography = {
  fontFamily: {
    // Using BricolageGrotesque as primary font family
    regular: 'BricolageGrotesque-Regular',
    medium: 'BricolageGrotesque-Medium',
    semibold: 'BricolageGrotesque-SemiBold',
    bold: 'BricolageGrotesque-Bold',

    // Ubuntu as secondary font family for specific use cases
    ubuntu: {
      regular: 'Ubuntu-Regular',
      medium: 'Ubuntu-Medium',
      bold: 'Ubuntu-Bold',
    },
  },
  fontSizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
  },
  fontWeights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.6,
  },
};

// Spacing system
const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
};

// Border radius system
const BorderRadius = {
  sm: 6,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  full: 9999,
};

// Export all constants for named imports
export { BorderRadius, Colors, Spacing, Typography };

// Default export
export default Colors;
