import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { BorderRadius, Colors, Spacing, Typography } from "@/constants/Colors";
import { useGlobalContext } from "@/contexts/GlobalContext";
import { router } from "expo-router";
import React, { useEffect } from "react";
import {
    ActivityIndicator,
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from "react-native";

export default function CartScreen() {
  const {
    cartItems,
    cartTotal,
    cartCount,
    updateCartQuantity,
    removeFromCart,
    isLoggedIn,
    isLoading,
    refreshCart,
  } = useGlobalContext();

  useEffect(() => {
    if (isLoggedIn) {
      refreshCart();
    }
  }, [isLoggedIn, refreshCart]);

  const handleUpdateQuantity = async (
    cartItemId: string,
    currentQuantity: number,
    change: number
  ) => {
    const newQuantity = currentQuantity + change;
    try {
      await updateCartQuantity(cartItemId, newQuantity);
    } catch (err) {
      console.error("Failed to update quantity:", err);
      Alert.alert("Error", "Failed to update quantity");
    }
  };

  const handleRemoveItem = async (cartItemId: string) => {
    try {
      await removeFromCart(cartItemId);
    } catch (err) {
      console.error("Failed to remove item:", err);
      Alert.alert("Error", "Failed to remove item");
    }
  };

  const subtotal = cartTotal;
  const deliveryFee = 50;
  const total = subtotal >= 1000 ? subtotal : subtotal + deliveryFee;

  if (!isLoggedIn) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">Your Cart</ThemedText>
        </ThemedView>
        <ThemedView style={styles.emptyCart}>
          <IconSymbol name="cart" size={64} color="#ccc" />
          <ThemedText type="subtitle" style={styles.emptyText}>
            Please sign in to view your cart
          </ThemedText>
          <TouchableOpacity
            style={styles.signInButton}
            onPress={() => router.push("/(tabs)/profile")}
          >
            <ThemedText style={styles.signInText}>Sign In</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    );
  }

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">Your Cart</ThemedText>
        </ThemedView>
        <ThemedView style={styles.emptyCart}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <ThemedText style={styles.emptySubtext}>
            Loading your cart...
          </ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerContent}>
          <Image
            source={require("@/assets/images/jujafresh-logo.jpeg")}
            style={styles.headerLogo}
          />
          <ThemedView style={styles.headerText}>
            <ThemedText type="title">Your Cart</ThemedText>
            <ThemedText type="default" style={styles.subtitle}>
              {cartItems.length} items • Delivery to Juja
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      {cartItems.length === 0 ? (
        <ThemedView style={styles.emptyCart}>
          <IconSymbol name="cart" size={64} color="#ccc" />
          <ThemedText type="subtitle" style={styles.emptyText}>
            Your cart is empty
          </ThemedText>
          <ThemedText style={styles.emptySubtext}>
            Add some items to get started
          </ThemedText>
        </ThemedView>
      ) : (
        <>
          <ScrollView
            style={styles.itemsList}
            showsVerticalScrollIndicator={false}
          >
            {cartItems.map((item) => (
              <View key={item._id} style={styles.cartItem}>
                {/* Product Image and Basic Info */}
                <View style={styles.itemHeader}>
                  <Image
                    source={{ uri: item.productImage }}
                    style={styles.itemImage}
                  />
                  <View style={styles.itemInfo}>
                    <ThemedText type="defaultSemiBold" style={styles.itemName}>
                      {item.productName}
                    </ThemedText>
                    <ThemedText style={styles.itemPrice}>
                      KSh {item.productPrice} per {item.productUnit}
                    </ThemedText>
                  </View>
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => handleRemoveItem(item._id)}
                  >
                    <IconSymbol name="trash" size={16} color="#FF5722" />
                  </TouchableOpacity>
                </View>

                {/* Quantity Controls and Total */}
                <View style={styles.itemFooter}>
                  <View style={styles.quantitySection}>
                    <ThemedText style={styles.quantityLabel}>
                      Quantity
                    </ThemedText>
                    <View style={styles.quantityControls}>
                      <TouchableOpacity
                        style={styles.quantityButton}
                        onPress={() =>
                          handleUpdateQuantity(item._id, item.quantity, -1)
                        }
                      >
                        <IconSymbol name="minus" size={14} color="#666" />
                      </TouchableOpacity>
                      <ThemedText style={styles.quantity}>
                        {item.quantity}
                      </ThemedText>
                      <TouchableOpacity
                        style={styles.quantityButton}
                        onPress={() =>
                          handleUpdateQuantity(item._id, item.quantity, 1)
                        }
                      >
                        <IconSymbol name="plus" size={14} color="#666" />
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View style={styles.totalSection}>
                    <ThemedText style={styles.itemTotalLabel}>Total</ThemedText>
                    <ThemedText style={styles.itemTotal}>
                      KSh {(item.productPrice * item.quantity).toLocaleString()}
                    </ThemedText>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>

          <View style={styles.summary}>
            <View style={styles.summaryCard}>
              <ThemedText style={styles.summaryTitle}>Order Summary</ThemedText>

              <View style={styles.summaryRow}>
                <ThemedText style={styles.summaryLabel}>
                  Subtotal ({cartCount} items)
                </ThemedText>
                <ThemedText style={styles.summaryValue}>
                  KSh {subtotal.toLocaleString()}
                </ThemedText>
              </View>

              <View style={styles.summaryRow}>
                <ThemedText style={styles.summaryLabel}>
                  Delivery Fee
                </ThemedText>
                <ThemedText style={styles.summaryValue}>
                  {subtotal >= 1000 ? (
                    <>
                      <ThemedText style={styles.freeDelivery}>FREE</ThemedText>
                      <ThemedText style={styles.originalPrice}>
                        {" "}
                        KSh {deliveryFee}
                      </ThemedText>
                    </>
                  ) : (
                    `KSh ${deliveryFee}`
                  )}
                </ThemedText>
              </View>

              {subtotal >= 1000 && (
                <View style={styles.savingsRow}>
                  <ThemedText style={styles.savingsText}>
                    🎉 You saved KSh {deliveryFee} on delivery!
                  </ThemedText>
                </View>
              )}

              <View style={styles.divider} />

              <View style={[styles.summaryRow, styles.totalRow]}>
                <ThemedText style={styles.totalLabel}>Total Amount</ThemedText>
                <ThemedText style={styles.totalValue}>
                  KSh {(subtotal >= 1000 ? subtotal : total).toLocaleString()}
                </ThemedText>
              </View>
            </View>

            <TouchableOpacity
              style={styles.checkoutButton}
              activeOpacity={0.9}
              onPress={() => router.push("/checkout")}
            >
              <ThemedText style={styles.checkoutText}>
                Proceed to Checkout • KSh{" "}
                {(subtotal >= 1000 ? subtotal : total).toLocaleString()}
              </ThemedText>
            </TouchableOpacity>
          </View>
        </>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: 60,
    paddingBottom: Spacing.xl,
    backgroundColor: Colors.dark.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.dark.border,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerLogo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: Spacing.md,
  },
  headerText: {
    flex: 1,
  },
  subtitle: {
    marginTop: Spacing.sm,
    color: Colors.dark.textSecondary,
    fontSize: Typography.fontSizes.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  emptyCart: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing["4xl"],
  },
  emptyText: {
    marginTop: Spacing.lg,
    textAlign: "center",
    fontSize: Typography.fontSizes.lg,
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.medium,
  },
  emptySubtext: {
    marginTop: Spacing.sm,
    textAlign: "center",
    color: Colors.dark.textSecondary,
    fontSize: Typography.fontSizes.base,
    fontFamily: Typography.fontFamily.regular,
  },
  itemsList: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.lg,
  },

  // Cart Item Styles
  cartItem: {
    backgroundColor: Colors.dark.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  itemHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: Spacing.lg,
  },
  itemImage: {
    width: 70,
    height: 70,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.lg,
  },
  itemInfo: {
    flex: 1,
    paddingRight: Spacing.md,
  },
  itemName: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.dark.text,
    marginBottom: Spacing.xs,
    lineHeight: Typography.fontSizes.base * Typography.lineHeights.tight,
    fontFamily: Typography.fontFamily.semibold,
  },
  itemPrice: {
    fontSize: Typography.fontSizes.sm,
    color: Colors.dark.textSecondary,
    lineHeight: Typography.fontSizes.sm * Typography.lineHeights.normal,
    fontFamily: Typography.fontFamily.regular,
  },
  removeButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
    backgroundColor: "rgba(255, 87, 34, 0.1)",
  },

  // Item Footer Styles
  itemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.borderLight,
  },
  quantitySection: {
    alignItems: "flex-start",
  },
  quantityLabel: {
    fontSize: Typography.fontSizes.xs,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.sm,
    fontWeight: Typography.fontWeights.medium,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.dark.surfaceSecondary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.dark.surface,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  quantity: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.dark.text,
    minWidth: 40,
    textAlign: "center",
    paddingHorizontal: Spacing.md,
    fontFamily: Typography.fontFamily.semibold,
  },
  totalSection: {
    alignItems: "flex-end",
  },
  itemTotalLabel: {
    fontSize: Typography.fontSizes.xs,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.xs,
    fontWeight: Typography.fontWeights.medium,
  },
  itemTotal: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.bold,
    color: Colors.dark.success,
    fontFamily: Typography.fontFamily.bold,
  },

  // Summary Styles
  summary: {
    backgroundColor: Colors.dark.surface,
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing["2xl"],
    borderTopWidth: 1,
    borderTopColor: Colors.dark.border,
  },
  summaryCard: {
    backgroundColor: Colors.dark.backgroundSecondary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  summaryTitle: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.light.text,
    marginBottom: Spacing.lg,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Spacing.md,
  },
  summaryLabel: {
    fontSize: Typography.fontSizes.base,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  summaryValue: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.medium,
    color: Colors.light.text,
    textAlign: "right",
  },
  freeDelivery: {
    color: Colors.light.success,
    fontWeight: Typography.fontWeights.bold,
  },
  originalPrice: {
    textDecorationLine: "line-through",
    color: Colors.light.textSecondary,
    fontSize: Typography.fontSizes.sm,
  },
  savingsRow: {
    backgroundColor: "rgba(76, 175, 80, 0.1)",
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  savingsText: {
    fontSize: Typography.fontSizes.sm,
    color: Colors.light.success,
    fontWeight: Typography.fontWeights.medium,
    textAlign: "center",
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: Spacing.md,
  },
  totalRow: {
    paddingTop: Spacing.md,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.light.text,
  },
  totalValue: {
    fontSize: Typography.fontSizes.xl,
    fontWeight: Typography.fontWeights.bold,
    color: Colors.light.success,
  },
  checkoutButton: {
    backgroundColor: Colors.dark.success,
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
  },
  checkoutText: {
    color: "#FFFFFF",
    textAlign: "center",
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
  },
  signInButton: {
    backgroundColor: Colors.dark.success,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    marginTop: Spacing.lg,
  },
  signInText: {
    color: "#FFFFFF",
    textAlign: "center",
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
  },
});
