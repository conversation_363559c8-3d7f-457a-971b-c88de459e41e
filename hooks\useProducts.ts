import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';

export const useProducts = () => {
  const products = useQuery(api.products.getAllProducts);
  
  return {
    products: products || [],
    loading: products === undefined,
    error: null, // Convex handles errors automatically
    refetch: () => {}, // Convex automatically refetches
  };
};

export const useFeaturedProducts = () => {
  const products = useQuery(api.products.getFeaturedProducts);
  
  return {
    products: products || [],
    loading: products === undefined,
    error: null,
    refetch: () => {},
  };
};

export const useProduct = (productId: string) => {
  const product = useQuery(api.products.getProductById,
    productId ? { productId: productId as any } : "skip"
  );
  
  return {
    product,
    loading: product === undefined && productId !== "",
    error: null,
    refetch: () => {},
  };
};

export const useProductsByCategory = (categoryId: string) => {
  const products = useQuery(api.products.getProductsByCategory,
    categoryId ? { categoryId } : "skip"
  );

  return {
    products: products || [],
    loading: products === undefined && categoryId !== "",
    error: null,
    refetch: () => {},
  };
};

export const useProductSearch = () => {
  // For search, we'll use a different approach since it requires parameters
  // This will be handled in components directly using useMutation or useQuery with parameters
  return {
    products: [],
    loading: false,
    error: null,
    searchProducts: async (query: string) => {
      // This will be implemented in components using useQuery with parameters
      console.log('Search products:', query);
    },
  };
};
