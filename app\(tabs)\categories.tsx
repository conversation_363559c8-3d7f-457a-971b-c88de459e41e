import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useCategories } from '@/hooks/useCategories';
import { router } from 'expo-router';
import React from 'react';
import { ActivityIndicator, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

// Fallback categories for when database is not available
const fallbackCategories = [
  { _id: 'fallback-1', name: 'Fresh Produce', icon: 'leaf.fill', color: '#4CAF50' },
  { _id: 'fallback-2', name: 'Dairy & Eggs', icon: 'drop.fill', color: '#2196F3' },
  { _id: 'fallback-3', name: 'Meat & Poultry', icon: 'flame.fill', color: '#FF5722' },
  { _id: 'fallback-4', name: 'Seafood', icon: 'fish.fill', color: '#00BCD4' },
  { _id: 'fallback-5', name: '<PERSON><PERSON>', icon: 'birthday.cake.fill', color: '#FF9800' },
  { _id: 'fallback-6', name: 'Frozen Foods', icon: 'snowflake', color: '#03A9F4' },
  { _id: 'fallback-7', name: 'Pantry Staples', icon: 'archivebox.fill', color: '#795548' },
  { _id: 'fallback-8', name: 'Beverages', icon: 'cup.and.saucer.fill', color: '#9C27B0' },
  { _id: 'fallback-9', name: 'Snacks & Sweets', icon: 'bag.fill', color: '#E91E63' },
  { _id: 'fallback-10', name: 'Personal Care', icon: 'heart.fill', color: '#F44336' },
  { _id: 'fallback-11', name: 'Household Items', icon: 'house.fill', color: '#607D8B' },
  { _id: 'fallback-12', name: 'Cleaning Supplies', icon: 'sparkles', color: '#8BC34A' },
];

export default function CategoriesScreen() {
  const { categories: dbCategories, loading } = useCategories();

  // Use database categories if available, otherwise use fallback
  const categories = dbCategories.length > 0 ? dbCategories : fallbackCategories;

  const handleCategoryPress = (category: any) => {
    router.push({
      pathname: '/category/[id]',
      params: {
        id: category._id,
        name: encodeURIComponent(category.name)
      }
    });
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Shop by Category</ThemedText>
        <ThemedText type="default" style={styles.subtitle}>
          Find everything you need for your home
        </ThemedText>
      </ThemedView>

      {loading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <ThemedText style={styles.loadingText}>Loading categories...</ThemedText>
        </ThemedView>
      ) : (
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <ThemedView style={styles.categoriesGrid}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category._id}
                style={styles.categoryCard}
                onPress={() => handleCategoryPress(category)}
              >
                <ThemedView style={[styles.iconContainer, { backgroundColor: category.color }]}>
                  <IconSymbol name={category.icon as any} size={32} color="white" />
                </ThemedView>
                <ThemedText type="defaultSemiBold" style={styles.categoryName}>
                  {category.name}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ThemedView>
        </ScrollView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  subtitle: {
    marginTop: 8,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    gap: 16,
  },
  categoryCard: {
    width: '47%',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryName: {
    textAlign: 'center',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    opacity: 0.7,
  },
});