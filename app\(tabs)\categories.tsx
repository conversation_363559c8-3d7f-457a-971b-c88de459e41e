import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

const categories = [
  { id: 1, name: 'Fresh Produce', icon: 'leaf.fill', color: '#4CAF50' },
  { id: 2, name: 'Dairy & Eggs', icon: 'drop.fill', color: '#2196F3' },
  { id: 3, name: 'Meat & Poultry', icon: 'flame.fill', color: '#FF5722' },
  { id: 4, name: 'Seafood', icon: 'fish.fill', color: '#00BCD4' },
  { id: 5, name: '<PERSON><PERSON>', icon: 'birthday.cake.fill', color: '#FF9800' },
  { id: 6, name: 'Frozen Foods', icon: 'snowflake', color: '#03A9F4' },
  { id: 7, name: 'Pantry Staples', icon: 'archivebox.fill', color: '#795548' },
  { id: 8, name: 'Beverages', icon: 'cup.and.saucer.fill', color: '#9C27B0' },
  { id: 9, name: 'Snacks & Sweets', icon: 'bag.fill', color: '#E91E63' },
  { id: 10, name: 'Personal Care', icon: 'heart.fill', color: '#F44336' },
  { id: 11, name: 'Household Items', icon: 'house.fill', color: '#607D8B' },
  { id: 12, name: 'Cleaning Supplies', icon: 'sparkles', color: '#8BC34A' },
];

export default function CategoriesScreen() {
  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Shop by Category</ThemedText>
        <ThemedText type="default" style={styles.subtitle}>
          Find everything you need for your home
        </ThemedText>
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.categoriesGrid}>
          {categories.map((category) => (
            <TouchableOpacity key={category.id} style={styles.categoryCard}>
              <ThemedView style={[styles.iconContainer, { backgroundColor: category.color }]}>
                <IconSymbol name={category.icon as any} size={32} color="white" />
              </ThemedView>
              <ThemedText type="defaultSemiBold" style={styles.categoryName}>
                {category.name}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  subtitle: {
    marginTop: 8,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    gap: 16,
  },
  categoryCard: {
    width: '47%',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryName: {
    textAlign: 'center',
    fontSize: 14,
  },
});