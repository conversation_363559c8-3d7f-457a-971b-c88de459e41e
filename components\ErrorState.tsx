import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { AppError, getErrorActionText, isRecoverableError } from '@/lib/utils/errorHandler';
import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';

interface ErrorStateProps {
  error: AppError | string;
  onRetry?: () => void;
  style?: any;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  onRetry,
  style,
}) => {
  const appError = typeof error === 'string' 
    ? { message: error, type: 'unknown' as const }
    : error;

  const canRetry = isRecoverableError(appError) && onRetry;
  const actionText = getErrorActionText(appError);

  const getErrorIcon = () => {
    switch (appError.type) {
      case 'network':
        return 'wifi.slash';
      case 'auth':
        return 'person.crop.circle.badge.exclamationmark';
      case 'server':
        return 'server.rack';
      default:
        return 'exclamationmark.triangle';
    }
  };

  return (
    <ThemedView style={[styles.container, style]}>
      <IconSymbol 
        name={getErrorIcon()} 
        size={64} 
        color={Colors.light.textSecondary} 
      />
      
      <ThemedText style={styles.title}>
        {appError.type === 'network' ? 'Connection Problem' : 'Something went wrong'}
      </ThemedText>
      
      <ThemedText style={styles.message}>
        {appError.message}
      </ThemedText>

      {canRetry && (
        <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
          <ThemedText style={styles.retryText}>
            {actionText}
          </ThemedText>
        </TouchableOpacity>
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
    color: Colors.light.text,
  },
  message: {
    textAlign: 'center',
    color: Colors.light.textSecondary,
    lineHeight: 20,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontWeight: '600',
  },
});
