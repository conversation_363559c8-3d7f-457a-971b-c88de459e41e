import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useGlobalContext } from '@/contexts/GlobalContext';
import { useQuery } from 'convex/react';
import { Image } from 'expo-image';
import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    StyleSheet,
    TouchableOpacity
} from 'react-native';
import { api } from '../../convex/_generated/api';

export default function CategoryProductsScreen() {
  const { id, name } = useLocalSearchParams<{ id: string; name: string }>();
  const { addToCart, isLoggedIn } = useGlobalContext();
  
  // Query products by category
  const products = useQuery(api.products.getProductsByCategory, 
    id ? { categoryId: id } : "skip"
  );
  
  const isLoading = products === undefined;

  const handleAddToCart = async (product: any) => {
    if (!isLoggedIn) {
      Alert.alert('Sign In Required', 'Please sign in to add items to your cart', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign In', onPress: () => router.push('/(tabs)/profile') }
      ]);
      return;
    }

    try {
      await addToCart(
        product._id,
        product.name,
        product.price,
        product.image,
        product.unit
      );
      Alert.alert('Success', `${product.name} added to cart!`);
    } catch (err) {
      console.error('Failed to add to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  const renderProduct = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.productCard}
      onPress={() => router.push(`/product/${item._id}`)}
    >
      {item.discount && item.discount > 0 && (
        <ThemedView style={styles.discountBadge}>
          <ThemedText style={styles.discountText}>{item.discount}% OFF</ThemedText>
        </ThemedView>
      )}
      
      <Image
        source={{ uri: item.image }}
        style={styles.productImage}
        contentFit="cover"
        placeholder="https://via.placeholder.com/200x120/f0f0f0/cccccc?text=Loading"
        transition={200}
      />
      
      <ThemedView style={styles.productInfo}>
        <ThemedText style={styles.productName} numberOfLines={2}>
          {item.name}
        </ThemedText>
        
        <ThemedText style={styles.productDescription} numberOfLines={2}>
          {item.description}
        </ThemedText>
        
        <ThemedView style={styles.priceRow}>
          <ThemedView>
            <ThemedText style={styles.price}>
              KSh {item.price.toLocaleString()}
            </ThemedText>
            <ThemedText style={styles.unit}>per {item.unit}</ThemedText>
          </ThemedView>
          
          <TouchableOpacity 
            style={styles.addButton}
            onPress={() => handleAddToCart(item)}
          >
            <IconSymbol name="plus" size={16} color="white" />
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={24} color="#000" />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            {decodeURIComponent(name || 'Category')}
          </ThemedText>
          <ThemedView style={styles.placeholder} />
        </ThemedView>
        
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <ThemedText style={styles.loadingText}>Loading products...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#000" />
        </TouchableOpacity>
        <ThemedText type="title" style={styles.headerTitle}>
          {decodeURIComponent(name || 'Category')}
        </ThemedText>
        <ThemedView style={styles.placeholder} />
      </ThemedView>

      {products && products.length > 0 ? (
        <FlatList
          data={products}
          renderItem={renderProduct}
          keyExtractor={(item) => item._id}
          numColumns={2}
          contentContainerStyle={styles.productsContainer}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <ThemedView style={styles.emptyContainer}>
          <IconSymbol name="tray" size={64} color="#ccc" />
          <ThemedText type="subtitle" style={styles.emptyText}>
            No products found in this category
          </ThemedText>
          <ThemedText style={styles.emptySubtext}>
            Check back later for new items!
          </ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: Colors.light.background,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    color: Colors.light.textSecondary,
  },
  productsContainer: {
    padding: 20,
  },
  row: {
    justifyContent: 'space-between',
  },
  productCard: {
    width: '48%',
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FF5722',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    zIndex: 1,
  },
  discountText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  productImage: {
    width: '100%',
    height: 120,
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.tint,
  },
  unit: {
    fontSize: 10,
    color: Colors.light.textSecondary,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.tint,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    marginTop: 16,
    textAlign: 'center',
    color: Colors.light.text,
  },
  emptySubtext: {
    marginTop: 8,
    textAlign: 'center',
    color: Colors.light.textSecondary,
  },
});
