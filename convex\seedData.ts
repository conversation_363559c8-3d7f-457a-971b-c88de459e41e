import { mutation } from "./_generated/server";

// Seed sample data for testing
export const seedDatabase = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if data already exists
    const existingCategories = await ctx.db.query("categories").collect();
    if (existingCategories.length > 0) {
      return { message: "Database already seeded" };
    }

    // Create categories
    const vegetablesCategory = await ctx.db.insert("categories", {
      name: "Vegetables",
      icon: "leaf.fill",
      color: "#4CAF50",
      description: "Fresh vegetables",
    });

    const fruitsCategory = await ctx.db.insert("categories", {
      name: "Fruits",
      icon: "apple.logo",
      color: "#FF9800",
      description: "Fresh fruits",
    });

    const dairyCategory = await ctx.db.insert("categories", {
      name: "Dairy",
      icon: "drop.fill",
      color: "#2196F3",
      description: "Dairy products",
    });

    const grainsCategory = await ctx.db.insert("categories", {
      name: "Grains",
      icon: "circle.grid.3x3.fill",
      color: "#795548",
      description: "Grains and cereals",
    });

    // Create products
    const products = [
      // Vegetables
      {
        name: "Fresh Tomatoes",
        description: "Locally grown fresh tomatoes, perfect for cooking and salads",
        price: 150,
        unit: "kg",
        category: vegetablesCategory,
        image: "https://images.unsplash.com/photo-1546470427-e5ac89c8ba3b?w=400",
        inStock: true,
        stockQuantity: 50,
        discount: 0,
        featured: true,
      },
      {
        name: "Green Spinach",
        description: "Fresh organic spinach leaves, rich in iron and vitamins",
        price: 80,
        unit: "bunch",
        category: vegetablesCategory,
        image: "https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400",
        inStock: true,
        stockQuantity: 30,
        discount: 10,
        featured: false,
      },
      {
        name: "Red Onions",
        description: "Fresh red onions, essential for cooking",
        price: 120,
        unit: "kg",
        category: vegetablesCategory,
        image: "https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400",
        inStock: true,
        stockQuantity: 40,
        discount: 0,
        featured: false,
      },
      {
        name: "Green Peppers",
        description: "Fresh green bell peppers, crisp and flavorful",
        price: 200,
        unit: "kg",
        category: vegetablesCategory,
        image: "https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400",
        inStock: true,
        stockQuantity: 25,
        discount: 0,
        featured: true,
      },

      // Fruits
      {
        name: "Sweet Bananas",
        description: "Sweet ripe bananas, perfect for snacking",
        price: 80,
        unit: "bunch",
        category: fruitsCategory,
        image: "https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400",
        inStock: true,
        stockQuantity: 60,
        discount: 0,
        featured: true,
      },
      {
        name: "Fresh Oranges",
        description: "Juicy oranges packed with vitamin C",
        price: 180,
        unit: "kg",
        category: fruitsCategory,
        image: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400",
        inStock: true,
        stockQuantity: 35,
        discount: 15,
        featured: false,
      },
      {
        name: "Red Apples",
        description: "Crisp red apples, great for snacking",
        price: 250,
        unit: "kg",
        category: fruitsCategory,
        image: "https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400",
        inStock: true,
        stockQuantity: 45,
        discount: 0,
        featured: true,
      },
      {
        name: "Watermelon",
        description: "Sweet and refreshing watermelon",
        price: 100,
        unit: "piece",
        category: fruitsCategory,
        image: "https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=400",
        inStock: true,
        stockQuantity: 20,
        discount: 0,
        featured: false,
      },

      // Dairy
      {
        name: "Fresh Milk",
        description: "Fresh cow milk, rich in calcium",
        price: 60,
        unit: "liter",
        category: dairyCategory,
        image: "https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400",
        inStock: true,
        stockQuantity: 100,
        discount: 0,
        featured: true,
      },
      {
        name: "Greek Yogurt",
        description: "Creamy Greek yogurt, high in protein",
        price: 120,
        unit: "500g",
        category: dairyCategory,
        image: "https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400",
        inStock: true,
        stockQuantity: 50,
        discount: 0,
        featured: false,
      },
      {
        name: "Cheddar Cheese",
        description: "Aged cheddar cheese, perfect for sandwiches",
        price: 300,
        unit: "250g",
        category: dairyCategory,
        image: "https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400",
        inStock: true,
        stockQuantity: 30,
        discount: 5,
        featured: false,
      },

      // Grains
      {
        name: "Brown Rice",
        description: "Nutritious brown rice, perfect for healthy meals",
        price: 180,
        unit: "kg",
        category: grainsCategory,
        image: "https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400",
        inStock: true,
        stockQuantity: 80,
        discount: 0,
        featured: false,
      },
      {
        name: "Whole Wheat Flour",
        description: "Fresh whole wheat flour for baking",
        price: 150,
        unit: "kg",
        category: grainsCategory,
        image: "https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400",
        inStock: true,
        stockQuantity: 60,
        discount: 0,
        featured: false,
      },
      {
        name: "Quinoa",
        description: "Organic quinoa, superfood grain",
        price: 400,
        unit: "500g",
        category: grainsCategory,
        image: "https://images.unsplash.com/photo-1586444248902-2f64eddc13df?w=400",
        inStock: true,
        stockQuantity: 25,
        discount: 10,
        featured: true,
      },
    ];

    // Insert all products
    for (const product of products) {
      await ctx.db.insert("products", product);
    }

    return { 
      message: "Database seeded successfully",
      categoriesCreated: 4,
      productsCreated: products.length
    };
  },
});
