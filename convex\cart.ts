import { getAuthUserId } from "@convex-dev/auth/server";
import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Add item to cart
export const addToCart = mutation({
  args: {
    userId: v.string(),
    productId: v.id("products"),
    productName: v.string(),
    productPrice: v.number(),
    productImage: v.string(),
    productUnit: v.string(),
    quantity: v.number(),
  },
  handler: async (ctx, args) => {
    // Check if item already exists in cart
    const existingItem = await ctx.db
      .query("cart")
      .withIndex("by_user_product", (q) => 
        q.eq("userId", args.userId).eq("productId", args.productId)
      )
      .first();

    if (existingItem) {
      // Update existing item quantity
      await ctx.db.patch(existingItem._id, {
        quantity: existingItem.quantity + args.quantity,
      });
      return await ctx.db.get(existingItem._id);
    } else {
      // Create new cart item
      const cartItemId = await ctx.db.insert("cart", args);
      return await ctx.db.get(cartItemId);
    }
  },
});

// Get user's cart items
export const getCartItems = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();

    return cartItems;
  },
});

// Update cart item quantity
export const updateCartQuantity = mutation({
  args: {
    cartItemId: v.id("cart"),
    quantity: v.number(),
  },
  handler: async (ctx, args) => {
    if (args.quantity <= 0) {
      // Remove item if quantity is 0 or negative
      await ctx.db.delete(args.cartItemId);
      return null;
    }

    await ctx.db.patch(args.cartItemId, {
      quantity: args.quantity,
    });

    return await ctx.db.get(args.cartItemId);
  },
});

// Remove item from cart
export const removeFromCart = mutation({
  args: { cartItemId: v.id("cart") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.cartItemId);
  },
});

// Clear user's cart
export const clearCart = mutation({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Delete all cart items for the user
    await Promise.all(
      cartItems.map((item) => ctx.db.delete(item._id))
    );
  },
});

// Get cart count for user
export const getCartCount = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    return cartItems.reduce((total, item) => total + item.quantity, 0);
  },
});

// Get cart total for user
export const getCartTotal = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    return cartItems.reduce((total, item) => total + (item.productPrice * item.quantity), 0);
  },
});

// Authenticated versions of cart functions
export const addToMyCart = mutation({
  args: {
    productId: v.id("products"),
    productName: v.string(),
    productPrice: v.number(),
    productImage: v.string(),
    productUnit: v.string(),
    quantity: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if item already exists in cart
    const existingItem = await ctx.db
      .query("cart")
      .withIndex("by_user_product", (q) =>
        q.eq("userId", userId).eq("productId", args.productId)
      )
      .first();

    if (existingItem) {
      // Update existing item quantity
      await ctx.db.patch(existingItem._id, {
        quantity: existingItem.quantity + args.quantity,
      });
      return await ctx.db.get(existingItem._id);
    } else {
      // Create new cart item
      const cartItemId = await ctx.db.insert("cart", {
        userId,
        ...args,
      });
      return await ctx.db.get(cartItemId);
    }
  },
});

export const getMyCartItems = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .collect();

    return cartItems;
  },
});

export const clearMyCart = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const cartItems = await ctx.db
      .query("cart")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    // Delete all cart items for the user
    await Promise.all(
      cartItems.map((item) => ctx.db.delete(item._id))
    );
  },
});
