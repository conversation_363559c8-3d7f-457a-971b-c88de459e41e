import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all products
export const getAllProducts = query({
  args: {},
  handler: async (ctx) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_in_stock", (q) => q.eq("inStock", true))
      .order("desc")
      .collect();

    return products;
  },
});

// Get featured products
export const getFeaturedProducts = query({
  args: {},
  handler: async (ctx) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_featured", (q) => q.eq("featured", true))
      .filter((q) => q.eq(q.field("inStock"), true))
      .order("desc")
      .collect();

    return products;
  },
});

// Get product by ID
export const getProductById = query({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    return product;
  },
});

// Search products
export const searchProducts = query({
  args: { searchTerm: v.string() },
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .withSearchIndex("search_products", (q) =>
        q.search("name", args.searchTerm).eq("inStock", true)
      )
      .collect();

    return products;
  },
});

// Get products by category
export const getProductsByCategory = query({
  args: { categoryId: v.string() },
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("category", args.categoryId))
      .filter((q) => q.eq(q.field("inStock"), true))
      .collect();

    return products;
  },
});

// Create product (admin function)
export const createProduct = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    price: v.number(),
    unit: v.string(),
    category: v.string(),
    image: v.string(),
    inStock: v.boolean(),
    stockQuantity: v.number(),
    discount: v.optional(v.number()),
    featured: v.boolean(),
  },
  handler: async (ctx, args) => {
    const productId = await ctx.db.insert("products", args);
    return productId;
  },
});

// Update product
export const updateProduct = mutation({
  args: {
    productId: v.id("products"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    unit: v.optional(v.string()),
    category: v.optional(v.string()),
    image: v.optional(v.string()),
    inStock: v.optional(v.boolean()),
    stockQuantity: v.optional(v.number()),
    discount: v.optional(v.number()),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { productId, ...updates } = args;
    
    // Remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(productId, cleanUpdates);
    return await ctx.db.get(productId);
  },
});

// Delete product
export const deleteProduct = mutation({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.productId);
  },
});
