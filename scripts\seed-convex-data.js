#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🌱 Seeding Convex database with sample data...');

try {
  // Run the seed mutation
  const result = execSync('npx convex run seedData:seedDatabase', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('✅ Database seeded successfully!');
  console.log(result);
  
  console.log('\n📊 Sample data includes:');
  console.log('• 4 Categories: Vegetables, Fruits, Dairy, Grains');
  console.log('• 15 Products with images and details');
  console.log('• Featured products for homepage');
  console.log('• Realistic pricing in KES');
  
  console.log('\n🚀 You can now test the app with sample data!');
  
} catch (error) {
  console.error('❌ Error seeding database:', error.message);
  console.log('\n💡 Make sure Convex is running with: npm run convex:dev');
  process.exit(1);
}
