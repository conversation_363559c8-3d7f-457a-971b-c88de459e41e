# JujaFresh - On-Demand Supermarket Essentials Delivery App

A React Native/Expo app for on-demand delivery of fresh produce, meat, dairy, eggs, household items, and all your supermarket essentials in Juja, Kenya, built with Convex as the backend.

## Features

- 🛒 Browse and search products
- 🛍️ Add items to cart
- 👤 User authentication
- 📱 Cross-platform (iOS & Android)
- 🚚 Order tracking
- 💳 Multiple payment options

## Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Convex
- **Database**: Convex Database
- **Authentication**: Convex Auth
- **Real-time**: Convex Reactivity

## Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Convex account (sign up at https://convex.dev)

### 1. Clone and Install

```bash
git clone <repository-url>
cd jujafresh
npm install
```

### 2. Convex Setup

1. Initialize Convex in your project:

```bash
npx convex dev --configure=new
```

2. This will create a `.env.local` file with your Convex deployment URL.

3. The Convex functions and schema are already set up in the `convex/` directory.

### 3. Configuration

The app uses the following Convex configuration:
- **Database**: Convex managed database
- **Authentication**: Convex Auth with password provider
- **Real-time**: Automatic reactivity for live updates

### 4. Seed Database (Optional but Recommended)

To test the app with sample data:

```bash
# Seed the database with sample categories and products
npm run seed-data
```

This will create:
- 4 Categories: Vegetables, Fruits, Dairy, Grains
- 15+ Products with realistic pricing and images
- Featured products for the homepage

### 5. Run the App

```bash
# Start the development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android

# Start Convex development server (in a separate terminal)
npm run convex:dev
```

## Project Structure

```
├── app/                    # App screens and navigation
│   ├── (tabs)/            # Tab navigation screens
│   ├── product/           # Product detail screens
│   └── checkout.tsx       # Checkout screen
├── components/            # Reusable components
├── constants/             # App constants and styles
├── contexts/              # React contexts (Global state)
├── hooks/                 # Custom React hooks
├── lib/                   # Services and utilities
│   ├── appwrite.ts       # Appwrite configuration
│   └── services/         # API services
└── scripts/              # Database setup scripts
```

## Key Components

### Global Context
The app uses a global context (`contexts/GlobalContext.tsx`) to manage:
- User authentication state
- Cart state and operations
- Global app state

### Services
- **Auth Service**: User authentication with Convex Auth
- **Cart Service**: Real-time cart operations
- **Product Service**: Product and category management
- **Order Service**: Order processing and tracking

### Database Tables

1. **Users**: User profiles and information
2. **Products**: Product catalog with details
3. **Categories**: Product categories
4. **Cart**: User cart items
5. **Orders**: Order history and tracking

## Convex Functions

The app uses Convex functions for all backend operations:

- **Authentication**: Sign up, sign in, sign out with Convex Auth
- **Products**: Query and mutation functions for products
- **Cart**: Real-time cart operations with automatic updates
- **Orders**: Order creation and tracking

## Development

### Adding New Features

1. Create services in `lib/services/`
2. Add hooks in `hooks/`
3. Update global context if needed
4. Create UI components
5. Add navigation routes

### Database Schema

Run the setup script to create the following collections:
- Users (with user permissions)
- Products (public read, admin write)
- Categories (public read, admin write)
- Cart (user-specific permissions)
- Orders (user-specific permissions)

## Deployment

### Expo Build

```bash
# Build for production
expo build:android
expo build:ios
```

### Environment Variables

For production, consider using environment variables for:
- Appwrite endpoint
- Project ID
- API keys (server-side only)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please contact [<EMAIL>] or create an issue in the repository.