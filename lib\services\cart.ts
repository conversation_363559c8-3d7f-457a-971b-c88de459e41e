import { Id } from "../../convex/_generated/dataModel";

export interface CartItem {
  _id: Id<"cart">;
  userId: string;
  productId: Id<"products">;
  productName: string;
  productPrice: number;
  productImage: string;
  productUnit: string;
  quantity: number;
  _creationTime: number;
}

class CartService {
  private client: any;

  constructor() {
    this.client = null;
  }

  // Initialize with Convex client
  initialize(client: any) {
    this.client = client;
  }

  // Add item to cart (requires authentication)
  async addToCart(
    productId: string,
    productName: string,
    productPrice: number,
    productImage: string,
    productUnit: string,
    quantity: number = 1
  ): Promise<CartItem> {
    try {
      if (!this.client) {
        throw new Error('Cart service not initialized');
      }

      const cartItem = await this.client.mutation("cart:addToMyCart", {
        productId,
        productName,
        productPrice,
        productImage,
        productUnit,
        quantity,
      });

      return cartItem;
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  }

  // Get user's cart items (requires authentication)
  async getCartItems(): Promise<CartItem[]> {
    try {
      if (!this.client) {
        throw new Error('Cart service not initialized');
      }

      const cartItems = await this.client.query("cart:getMyCartItems");
      return cartItems || [];
    } catch (error) {
      console.error('Error fetching cart items:', error);
      return [];
    }
  }

  // Update cart item quantity
  async updateCartQuantity(cartItemId: string, quantity: number): Promise<CartItem | null> {
    try {
      if (!this.client) {
        throw new Error('Cart service not initialized');
      }

      const updatedItem = await this.client.mutation("cart:updateCartQuantity", {
        cartItemId,
        quantity,
      });

      return updatedItem;
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      throw error;
    }
  }

  // Remove item from cart
  async removeFromCart(cartItemId: string): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('Cart service not initialized');
      }

      await this.client.mutation("cart:removeFromCart", { cartItemId });
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  }

  // Clear user's cart (requires authentication)
  async clearCart(): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('Cart service not initialized');
      }

      await this.client.mutation("cart:clearMyCart");
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  }

  // Get cart count (requires authentication)
  async getCartCount(): Promise<number> {
    try {
      if (!this.client) {
        return 0;
      }

      const cartItems = await this.getCartItems();
      return cartItems.reduce((total, item) => total + item.quantity, 0);
    } catch (error) {
      console.error('Error getting cart count:', error);
      return 0;
    }
  }

  // Get cart total (requires authentication)
  async getCartTotal(): Promise<number> {
    try {
      if (!this.client) {
        return 0;
      }

      const cartItems = await this.getCartItems();
      return cartItems.reduce((total, item) => total + (item.productPrice * item.quantity), 0);
    } catch (error) {
      console.error('Error getting cart total:', error);
      return 0;
    }
  }
}

export default new CartService();
