import { CartItem } from '@/lib/services/cart';
import { useAuthActions } from "@convex-dev/auth/react";
import { useMutation, useQuery } from "convex/react";
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { api } from "../convex/_generated/api";

export interface User {
  _id: string;
  accountId: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  avatar?: string;
}

interface GlobalContextType {
  // Auth state
  user: User | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  
  // Cart state
  cartItems: CartItem[];
  cartCount: number;
  cartTotal: number;
  
  // Auth methods
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, phone?: string) => Promise<void>;
  signOut: () => Promise<void>;
  
  // Cart methods
  addToCart: (productId: any, productName: string, productPrice: number, productImage: string, productUnit: string, quantity?: number) => Promise<void>;
  updateCartQuantity: (cartItemId: any, quantity: number) => Promise<void>;
  removeFromCart: (cartItemId: any) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

export const useGlobalContext = () => {
  const context = useContext(GlobalContext);
  if (context === undefined) {
    throw new Error('useGlobalContext must be used within a GlobalProvider');
  }
  return context;
};

export const GlobalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  
  // Auth hooks
  const { signIn: convexSignIn, signOut: convexSignOut } = useAuthActions();
  
  // User queries and mutations
  const user = useQuery(api.users.getCurrentUser);
  const createUserProfile = useMutation(api.authHelpers.createUserProfile);
  
  // Cart queries and mutations
  const cartItemsQuery = useQuery(api.cart.getMyCartItems);
  const cartItems = useMemo(() => cartItemsQuery || [], [cartItemsQuery]);
  const addToCartMutation = useMutation(api.cart.addToMyCart);
  const updateCartQuantityMutation = useMutation(api.cart.updateCartQuantity);
  const removeFromCartMutation = useMutation(api.cart.removeFromCart);
  const clearCartMutation = useMutation(api.cart.clearMyCart);

  // Derived state (memoized for performance)
  const isLoggedIn = !!user;
  const cartCount = useMemo(() =>
    cartItems.reduce((sum, item) => sum + item.quantity, 0),
    [cartItems]
  );
  const cartTotal = useMemo(() =>
    cartItems.reduce((sum, item) => sum + (item.productPrice * item.quantity), 0),
    [cartItems]
  );

  // Initialize loading state
  useEffect(() => {
    // Set loading to false after a short delay to show splash screen
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Auth methods
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setIsLoading(true);
      await convexSignIn("password", { email, password, flow: "signIn" });
    } catch (error: any) {
      console.error('Error signing in:', error);
      // Provide user-friendly error messages
      const errorMessage = error?.message || 'Failed to sign in. Please check your credentials.';
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [convexSignIn]);

  const signUp = useCallback(async (email: string, password: string, name: string, phone?: string) => {
    try {
      setIsLoading(true);
      // Sign up with Convex Auth
      await convexSignIn("password", { email, password, name, flow: "signUp" });

      // Create user profile
      await createUserProfile({
        email,
        name,
        phone,
      });
    } catch (error: any) {
      console.error('Error signing up:', error);
      // Provide user-friendly error messages
      let errorMessage = 'Failed to create account. Please try again.';
      if (error?.message?.includes('already exists')) {
        errorMessage = 'An account with this email already exists.';
      } else if (error?.message?.includes('password')) {
        errorMessage = 'Password must be at least 8 characters long.';
      } else if (error?.message) {
        errorMessage = error.message;
      }
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [convexSignIn, createUserProfile]);

  const signOut = useCallback(async () => {
    try {
      setIsLoading(true);
      await convexSignOut();
    } catch (error: any) {
      console.error('Error signing out:', error);
      // Even if sign out fails, we should clear local state
      const errorMessage = error?.message || 'Failed to sign out properly, but you have been logged out locally.';
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [convexSignOut]);

  // Cart methods
  const addToCart = useCallback(async (
    productId: any,
    productName: string,
    productPrice: number,
    productImage: string,
    productUnit: string,
    quantity: number = 1
  ) => {
    try {
      await addToCartMutation({
        productId,
        productName,
        productPrice,
        productImage,
        productUnit,
        quantity,
      });
    } catch (error: any) {
      console.error('Error adding to cart:', error);
      const errorMessage = error?.message || 'Failed to add item to cart. Please try again.';
      throw new Error(errorMessage);
    }
  }, [addToCartMutation]);

  const updateCartQuantity = useCallback(async (cartItemId: any, quantity: number) => {
    try {
      await updateCartQuantityMutation({ cartItemId, quantity });
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      throw error;
    }
  }, [updateCartQuantityMutation]);

  const removeFromCart = useCallback(async (cartItemId: any) => {
    try {
      await removeFromCartMutation({ cartItemId });
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  }, [removeFromCartMutation]);

  const clearCart = useCallback(async () => {
    try {
      await clearCartMutation();
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  }, [clearCartMutation]);

  const refreshCart = useCallback(async () => {
    // Cart is automatically refreshed by Convex reactivity
    // This is a no-op for compatibility
  }, []);

  const value: GlobalContextType = {
    // Auth state
    user: user as User | null,
    isLoading,
    isLoggedIn,
    
    // Cart state
    cartItems,
    cartCount,
    cartTotal,
    
    // Auth methods
    signIn,
    signUp,
    signOut,
    
    // Cart methods
    addToCart,
    updateCartQuantity,
    removeFromCart,
    clearCart,
    refreshCart,
  };

  return (
    <GlobalContext.Provider value={value}>
      {children}
    </GlobalContext.Provider>
  );
};

export default GlobalProvider;
