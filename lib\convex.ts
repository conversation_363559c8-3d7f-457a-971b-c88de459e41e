import { ConvexReactClient } from "convex/react";
import { ConvexAuthProvider } from "@convex-dev/auth/react";

// Get environment variables with fallbacks
const getEnvVar = (key: string, fallback: string): string => {
  const value = process.env[key];
  if (!value) {
    console.warn(`Environment variable ${key} not found, using fallback: ${fallback}`);
    return fallback;
  }
  return value;
};

// Convex configuration
export const convexConfig = {
  url: getEnvVar('EXPO_PUBLIC_CONVEX_URL', 'https://artful-buffalo-183.convex.cloud'),
};

// Validate configuration
const validateConfig = () => {
  if (!convexConfig.url) {
    throw new Error('Missing required Convex configuration: EXPO_PUBLIC_CONVEX_URL');
  }
};

// Initialize Convex client
let client: ConvexReactClient;

try {
  validateConfig();
  client = new ConvexReactClient(convexConfig.url);
  
  console.log('✅ Convex client initialized successfully');
  console.log(`📡 URL: ${convexConfig.url}`);
} catch (error) {
  console.error('❌ Failed to initialize Convex client:', error);
  throw error;
}

export { client };
export default client;
