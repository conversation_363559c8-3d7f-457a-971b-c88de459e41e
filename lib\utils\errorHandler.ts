/**
 * Utility functions for handling errors and providing user-friendly messages
 */

export interface AppError {
  message: string;
  type: 'network' | 'auth' | 'validation' | 'server' | 'unknown';
  originalError?: any;
}

/**
 * Convert various error types to user-friendly messages
 */
export const handleError = (error: any): AppError => {
  // Network errors
  if (error?.message?.includes('network') || error?.message?.includes('fetch')) {
    return {
      message: 'Network error. Please check your internet connection and try again.',
      type: 'network',
      originalError: error,
    };
  }

  // Authentication errors
  if (error?.message?.includes('auth') || error?.message?.includes('unauthorized')) {
    return {
      message: 'Authentication failed. Please sign in again.',
      type: 'auth',
      originalError: error,
    };
  }

  // Validation errors
  if (error?.message?.includes('validation') || error?.message?.includes('invalid')) {
    return {
      message: error.message || 'Invalid input. Please check your data and try again.',
      type: 'validation',
      originalError: error,
    };
  }

  // Server errors
  if (error?.message?.includes('server') || error?.status >= 500) {
    return {
      message: 'Server error. Please try again later.',
      type: 'server',
      originalError: error,
    };
  }

  // Default error
  return {
    message: error?.message || 'Something went wrong. Please try again.',
    type: 'unknown',
    originalError: error,
  };
};

/**
 * Log errors for debugging while providing user-friendly messages
 */
export const logAndHandleError = (error: any, context?: string): AppError => {
  const appError = handleError(error);
  
  // Log the full error for debugging
  console.error(`Error in ${context || 'unknown context'}:`, {
    message: appError.message,
    type: appError.type,
    originalError: appError.originalError,
    stack: error?.stack,
  });

  return appError;
};

/**
 * Retry function with exponential backoff
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

/**
 * Check if error is recoverable (user can retry)
 */
export const isRecoverableError = (error: AppError): boolean => {
  return error.type === 'network' || error.type === 'server';
};

/**
 * Get appropriate action text for error type
 */
export const getErrorActionText = (error: AppError): string => {
  switch (error.type) {
    case 'network':
      return 'Check Connection';
    case 'auth':
      return 'Sign In';
    case 'validation':
      return 'Fix Input';
    case 'server':
      return 'Try Again';
    default:
      return 'Retry';
  }
};
