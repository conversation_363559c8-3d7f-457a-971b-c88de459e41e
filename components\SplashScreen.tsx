import { Colors, Typography } from '@/constants/Colors';
import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';

export default function SplashScreen() {
  return (
    <View style={styles.container}>
      <Image 
        source={require('@/assets/images/jujafresh-logo.jpeg')} 
        style={styles.logo}
      />
      <Text style={styles.title}>JujaFresh</Text>
      <Text style={styles.subtitle}>Fresh essentials delivered to your door</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.dark.background,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.dark.success,
    marginBottom: 8,
    fontFamily: Typography.fontFamily.bold,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.dark.textSecondary,
    textAlign: 'center',
    paddingHorizontal: 40,
    fontFamily: Typography.fontFamily.regular,
  },
});