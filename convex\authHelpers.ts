import { v } from "convex/values";
import { mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Create user profile after successful authentication
export const createUserProfile = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    phone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user profile already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_account_id", (q) => q.eq("accountId", userId))
      .first();

    if (existingUser) {
      return existingUser;
    }

    // Create new user profile
    const userProfileId = await ctx.db.insert("users", {
      accountId: userId,
      email: args.email,
      name: args.name,
      phone: args.phone,
      address: "",
      avatar: "",
    });

    return await ctx.db.get(userProfileId);
  },
});

// Update current user profile
export const updateCurrentUserProfile = mutation({
  args: {
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_account_id", (q) => q.eq("accountId", userId))
      .first();

    if (!user) {
      throw new Error("User profile not found");
    }

    // Remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(args).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(user._id, cleanUpdates);
    return await ctx.db.get(user._id);
  },
});
