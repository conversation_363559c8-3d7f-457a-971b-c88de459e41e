import { authTables } from "@convex-dev/auth/server";
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  ...authTables,
  // Users table
  users: defineTable({
    accountId: v.string(), // For linking with auth
    email: v.string(),
    name: v.string(),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    avatar: v.optional(v.string()),
  })
    .index("by_account_id", ["accountId"])
    .index("by_email", ["email"]),

  // Products table
  products: defineTable({
    name: v.string(),
    description: v.string(),
    price: v.number(),
    unit: v.string(),
    category: v.string(), // Category ID reference
    image: v.string(),
    inStock: v.boolean(),
    stockQuantity: v.number(),
    discount: v.optional(v.number()),
    featured: v.boolean(),
  })
    .index("by_category", ["category"])
    .index("by_featured", ["featured"])
    .index("by_in_stock", ["inStock"])
    .searchIndex("search_products", {
      searchField: "name",
      filterFields: ["inStock", "category"]
    }),

  // Categories table
  categories: defineTable({
    name: v.string(),
    icon: v.string(),
    color: v.string(),
    description: v.string(),
  }),

  // Cart items table
  cart: defineTable({
    userId: v.string(),
    productId: v.id("products"),
    productName: v.string(),
    productPrice: v.number(),
    productImage: v.string(),
    productUnit: v.string(),
    quantity: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_user_product", ["userId", "productId"]),

  // Orders table
  orders: defineTable({
    userId: v.string(),
    orderNumber: v.string(),
    items: v.array(v.object({
      productId: v.string(),
      productName: v.string(),
      productPrice: v.number(),
      productImage: v.string(),
      productUnit: v.string(),
      quantity: v.number(),
    })),
    subtotal: v.number(),
    deliveryFee: v.number(),
    total: v.number(),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("preparing"),
      v.literal("out_for_delivery"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    paymentMethod: v.union(
      v.literal("cash_on_delivery"),
      v.literal("mpesa"),
      v.literal("card")
    ),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("paid"),
      v.literal("failed")
    ),
    deliveryAddress: v.object({
      street: v.string(),
      city: v.string(),
      postalCode: v.string(),
      country: v.string(),
      coordinates: v.optional(v.object({
        latitude: v.number(),
        longitude: v.number(),
      })),
    }),
    customerNotes: v.optional(v.string()),
    estimatedDeliveryTime: v.optional(v.string()),
  })
    .index("by_user", ["userId"])
    .index("by_order_number", ["orderNumber"])
    .index("by_status", ["status"]),
});
